#!/bin/bash

# RAG Chatbot 设置向导脚本
# 提供交互式指导来创建 RAG chatbot 工作流

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}  GitHub API RAG Chatbot 设置向导${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    echo "本向导将指导您创建一个基于 GitHub API 文档的 RAG chatbot。"
    echo ""
    echo "您将学会如何："
    echo "• 索引 GitHub API 文档到向量数据库"
    echo "• 创建智能聊天界面"
    echo "• 配置 RAG 检索增强生成"
    echo ""
    read -p "按 Enter 键继续..."
}

# 检查前置条件
check_prerequisites() {
    log_step "检查前置条件..."
    
    # 检查 n8n 服务
    if curl -s http://localhost:5678 > /dev/null; then
        log_success "n8n 服务运行正常"
    else
        log_error "n8n 服务未运行，请先启动 n8n"
        echo "运行: ./scripts/start-n8n-mcp.sh start"
        exit 1
    fi
    
    # 检查必需的凭据
    echo ""
    log_info "请确保您已准备以下 API 凭据："
    echo "1. OpenAI API Key (用于 embeddings 和 chat)"
    echo "2. Pinecone API Key (用于向量数据库)"
    echo "3. Pinecone Index 名称: github-api-docs"
    echo ""
    
    read -p "是否已准备好所有凭据？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_warning "请先准备好所需的 API 凭据，然后重新运行此脚本"
        exit 1
    fi
}

# 打开 n8n Web UI
open_n8n_ui() {
    log_step "打开 n8n Web UI..."
    
    echo "即将打开 n8n Web 界面..."
    echo "URL: http://localhost:5678"
    echo ""
    
    # 尝试打开浏览器
    if command -v open > /dev/null; then
        open http://localhost:5678
    elif command -v xdg-open > /dev/null; then
        xdg-open http://localhost:5678
    else
        log_info "请手动打开浏览器访问: http://localhost:5678"
    fi
    
    echo ""
    read -p "按 Enter 键继续..."
}

# 指导创建工作流
guide_workflow_creation() {
    log_step "创建新工作流..."
    
    echo "在 n8n Web UI 中："
    echo "1. 点击 'New Workflow' 按钮"
    echo "2. 将工作流命名为 'GitHub API RAG Chatbot'"
    echo ""
    read -p "完成后按 Enter 键继续..."
}

# 指导数据索引部分
guide_data_indexing() {
    log_step "配置数据索引部分..."
    
    echo ""
    echo -e "${YELLOW}=== 第一部分：数据索引 ===${NC}"
    echo ""
    
    # HTTP Request 节点
    echo "1. 添加 HTTP Request 节点："
    echo "   • 名称: 'Fetch GitHub API Spec'"
    echo "   • Method: GET"
    echo "   • URL: https://raw.githubusercontent.com/github/rest-api-description/refs/heads/main/descriptions/api.github.com/api.github.com.json"
    echo ""
    read -p "完成后按 Enter 键继续..."
    
    # Pinecone Vector Store 节点
    echo "2. 添加 Pinecone Vector Store 节点："
    echo "   • 名称: 'Save to Pinecone Vector Store'"
    echo "   • Operation Mode: Insert Documents"
    echo "   • Pinecone Index: github-api-docs"
    echo "   • 配置 Pinecone 凭据"
    echo ""
    read -p "完成后按 Enter 键继续..."
    
    # OpenAI Embeddings 节点
    echo "3. 添加 OpenAI Embeddings 节点："
    echo "   • 名称: 'Generate Embeddings'"
    echo "   • Model: text-embedding-3-small"
    echo "   • 配置 OpenAI 凭据"
    echo ""
    read -p "完成后按 Enter 键继续..."
    
    # Default Data Loader 节点
    echo "4. 添加 Default Data Loader 节点："
    echo "   • 名称: 'Default Data Loader'"
    echo "   • 使用默认设置"
    echo ""
    read -p "完成后按 Enter 键继续..."
    
    # Text Splitter 节点
    echo "5. 添加 Recursive Character Text Splitter 节点："
    echo "   • 名称: 'Text Splitter'"
    echo "   • Chunk Size: 1000"
    echo "   • Chunk Overlap: 200"
    echo ""
    read -p "完成后按 Enter 键继续..."
    
    # 连接说明
    echo "6. 连接节点："
    echo "   • HTTP Request → Pinecone Vector Store (main)"
    echo "   • OpenAI Embeddings → Pinecone Vector Store (ai_embedding)"
    echo "   • Default Data Loader → Pinecone Vector Store (ai_document)"
    echo "   • Text Splitter → Default Data Loader (ai_textSplitter)"
    echo ""
    read -p "完成连接后按 Enter 键继续..."
}

# 指导聊天界面部分
guide_chat_interface() {
    log_step "配置聊天界面部分..."
    
    echo ""
    echo -e "${YELLOW}=== 第二部分：聊天界面 ===${NC}"
    echo ""
    
    # Chat Trigger 节点
    echo "1. 添加 Chat Trigger 节点："
    echo "   • 名称: 'Chat Trigger'"
    echo "   • 使用默认设置"
    echo ""
    read -p "完成后按 Enter 键继续..."
    
    # AI Agent 节点
    echo "2. 添加 AI Agent 节点："
    echo "   • 名称: 'AI Agent'"
    echo "   • Agent Type: Tools Agent"
    echo "   • System Message:"
    echo "     'You are a helpful assistant providing information about the GitHub API'"
    echo "     'and how to use it based on the OpenAPI V3 specifications.'"
    echo ""
    read -p "完成后按 Enter 键继续..."
    
    # 其他节点...
    echo "3. 继续添加其他必需节点..."
    echo "   详细步骤请参考: docs/RAG_CHATBOT_MANUAL_SETUP.md"
    echo ""
    read -p "完成后按 Enter 键继续..."
}

# 指导测试工作流
guide_testing() {
    log_step "测试工作流..."
    
    echo ""
    echo -e "${YELLOW}=== 测试工作流 ===${NC}"
    echo ""
    
    echo "1. 首先运行数据索引："
    echo "   • 点击 'Fetch GitHub API Spec' 节点"
    echo "   • 点击 'Execute Node'"
    echo "   • 等待数据处理完成（可能需要几分钟）"
    echo ""
    read -p "数据索引完成后按 Enter 键继续..."
    
    echo "2. 测试聊天功能："
    echo "   • 点击 'Chat Trigger' 节点"
    echo "   • 点击底部的 'Chat' 按钮"
    echo "   • 尝试问问题，例如："
    echo "     - 'How do I create a GitHub App from a manifest?'"
    echo "     - 'What are the authentication methods for GitHub API?'"
    echo ""
    read -p "测试完成后按 Enter 键继续..."
}

# 显示完成信息
show_completion() {
    clear
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}  🎉 RAG Chatbot 设置完成！${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    log_success "恭喜！您已成功创建了 GitHub API RAG Chatbot"
    echo ""
    echo "您的 chatbot 现在可以："
    echo "• 回答关于 GitHub API 的问题"
    echo "• 提供 API 使用示例"
    echo "• 解释 API 参数和认证方法"
    echo ""
    echo "下一步："
    echo "• 尝试更多问题来测试 chatbot"
    echo "• 根据需要调整系统提示词"
    echo "• 考虑添加更多数据源"
    echo ""
    echo "相关文档："
    echo "• 详细配置指南: docs/RAG_CHATBOT_MANUAL_SETUP.md"
    echo "• n8n Web UI: http://localhost:5678"
    echo ""
    log_info "感谢使用 RAG Chatbot 设置向导！"
}

# 主函数
main() {
    show_welcome
    check_prerequisites
    open_n8n_ui
    guide_workflow_creation
    guide_data_indexing
    guide_chat_interface
    guide_testing
    show_completion
}

# 执行主函数
main "$@"
