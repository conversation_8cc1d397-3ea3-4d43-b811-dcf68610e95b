#!/bin/bash

# RAG Chatbot 工作流部署脚本
# 将 RAG chatbot 工作流部署到 n8n 实例

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
N8N_URL="http://localhost:5678"
N8N_API_URL="${N8N_URL}/api/v1"
WORKFLOW_FILE="n8n-workflow/simple-test-workflow.json"

# 从 .env 文件读取 API key
get_api_key() {
    if [ -f ".env" ]; then
        N8N_API_KEY=$(grep "N8N_API_KEY" .env | cut -d '=' -f2)
        if [ -z "$N8N_API_KEY" ]; then
            log_error "无法从 .env 文件中读取 N8N_API_KEY"
            exit 1
        fi
    else
        log_error ".env 文件不存在"
        exit 1
    fi
}

# 检查 n8n 服务状态
check_n8n_service() {
    log_info "检查 n8n 服务状态..."
    
    response=$(curl -s -w "%{http_code}" -o /dev/null "$N8N_URL")
    
    if [ "$response" = "200" ] || [ "$response" = "401" ]; then
        log_success "n8n 服务运行正常"
    else
        log_error "n8n 服务不可访问 (HTTP $response)"
        exit 1
    fi
}

# 检查工作流文件
check_workflow_file() {
    log_info "检查工作流文件..."
    
    if [ ! -f "$WORKFLOW_FILE" ]; then
        log_error "工作流文件不存在: $WORKFLOW_FILE"
        exit 1
    fi
    
    # 验证 JSON 格式
    if ! jq empty "$WORKFLOW_FILE" 2>/dev/null; then
        log_error "工作流文件不是有效的 JSON 格式"
        exit 1
    fi
    
    log_success "工作流文件验证通过"
}

# 部署工作流
deploy_workflow() {
    log_info "部署 RAG Chatbot 工作流..."
    
    # 读取工作流文件
    workflow_data=$(cat "$WORKFLOW_FILE")
    
    # 创建工作流
    response=$(curl -s -w "%{http_code}" \
        -H "X-N8N-API-KEY: $N8N_API_KEY" \
        -H "Content-Type: application/json" \
        -X POST \
        -d "$workflow_data" \
        "$N8N_API_URL/workflows" \
        -o /tmp/n8n_response.json)
    
    if [ "$response" = "201" ] || [ "$response" = "200" ]; then
        workflow_id=$(jq -r '.id' /tmp/n8n_response.json 2>/dev/null || echo "unknown")
        log_success "工作流部署成功！"
        log_info "工作流 ID: $workflow_id"
        log_info "访问地址: $N8N_URL/workflow/$workflow_id"
        
        # 清理临时文件
        rm -f /tmp/n8n_response.json
        
        return 0
    else
        log_error "工作流部署失败 (HTTP $response)"
        if [ -f /tmp/n8n_response.json ]; then
            log_error "错误详情:"
            cat /tmp/n8n_response.json
            rm -f /tmp/n8n_response.json
        fi
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证工作流部署..."
    
    # 获取工作流列表
    response=$(curl -s -w "%{http_code}" \
        -H "X-N8N-API-KEY: $N8N_API_KEY" \
        "$N8N_API_URL/workflows" \
        -o /tmp/n8n_workflows.json)
    
    if [ "$response" = "200" ]; then
        # 检查是否包含我们的工作流
        if jq -e '.data[] | select(.name == "GitHub API RAG Chatbot")' /tmp/n8n_workflows.json > /dev/null 2>&1; then
            log_success "工作流验证成功"
        else
            log_warning "工作流可能未正确部署"
        fi
        rm -f /tmp/n8n_workflows.json
    else
        log_warning "无法验证工作流部署状态"
    fi
}

# 显示使用说明
show_usage_instructions() {
    log_info "RAG Chatbot 使用说明:"
    echo ""
    echo "1. 配置必需的凭据:"
    echo "   - OpenAI API Key (用于 GPT-4o-mini 和 embeddings)"
    echo "   - Pinecone API Key 和 Index (github-api-docs)"
    echo ""
    echo "2. 首次运行数据索引部分:"
    echo "   - 执行 'Fetch GitHub API Spec' 节点开始的流程"
    echo "   - 等待数据索引完成 (可能需要几分钟)"
    echo ""
    echo "3. 使用聊天功能:"
    echo "   - 点击 'Chat Trigger' 节点的聊天按钮"
    echo "   - 开始与 GitHub API 文档聊天"
    echo ""
    echo "4. 示例问题:"
    echo "   - 'How do I create a GitHub App from a manifest?'"
    echo "   - 'What are the authentication methods for GitHub API?'"
    echo "   - 'How do I list repositories for a user?'"
    echo ""
    log_info "访问 n8n Web UI: $N8N_URL"
}

# 主函数
main() {
    log_info "开始部署 RAG Chatbot 工作流..."
    echo ""
    
    # 获取 API key
    get_api_key
    
    # 检查服务和文件
    check_n8n_service
    check_workflow_file
    echo ""
    
    # 部署工作流
    deploy_workflow
    echo ""
    
    # 验证部署
    verify_deployment
    echo ""
    
    # 显示使用说明
    show_usage_instructions
    echo ""
    
    log_success "RAG Chatbot 工作流部署完成！"
}

# 执行主函数
main "$@"
