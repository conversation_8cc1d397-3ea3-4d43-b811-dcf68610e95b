# GitHub API RAG Chatbot 手动配置指南

根据 n8n 博客文章 https://blog.n8n.io/rag-chatbot/ 的指导，本文档提供了在 n8n 中手动创建 RAG chatbot 工作流的详细步骤。

## 前置条件

在开始之前，请确保您已经配置了以下凭据：

1. **OpenAI API Key** - 用于生成 embeddings 和 chat 响应
2. **Pinecone API Key** - 用于向量数据库存储和检索
3. **Pinecone Index** - 创建名为 `github-api-docs` 的索引

## 工作流架构

RAG chatbot 工作流包含两个主要部分：

### 第一部分：数据索引（Data Indexing）
1. HTTP Request → Pinecone Vector Store (Insert)
2. 数据流：GitHub API Spec → 文本分割 → 生成 Embeddings → 存储到 Pinecone

### 第二部分：聊天界面（Chat Interface）
1. Chat Trigger → AI Agent → Vector Store Tool → Pinecone Vector Store (Retrieve)
2. 数据流：用户问题 → 向量搜索 → 检索相关文档 → 生成回答

## 详细配置步骤

### 步骤 1: 创建新工作流

1. 访问 n8n Web UI: http://localhost:5678
2. 点击 "New Workflow"
3. 将工作流命名为 "GitHub API RAG Chatbot"

### 步骤 2: 配置数据索引部分

#### 2.1 添加 HTTP Request 节点
- **节点类型**: HTTP Request
- **名称**: "Fetch GitHub API Spec"
- **配置**:
  - Method: GET
  - URL: `https://raw.githubusercontent.com/github/rest-api-description/refs/heads/main/descriptions/api.github.com/api.github.com.json`

#### 2.2 添加 Pinecone Vector Store 节点（插入模式）
- **节点类型**: Pinecone Vector Store
- **名称**: "Save to Pinecone Vector Store"
- **配置**:
  - Operation Mode: Insert Documents
  - Pinecone Index: `github-api-docs`
  - 连接 Pinecone 凭据

#### 2.3 添加 OpenAI Embeddings 节点（用于插入）
- **节点类型**: OpenAI Embeddings
- **名称**: "Generate Embeddings"
- **配置**:
  - Model: `text-embedding-3-small`
  - 连接 OpenAI 凭据

#### 2.4 添加 Default Data Loader 节点
- **节点类型**: Default Data Loader
- **名称**: "Default Data Loader"
- **配置**: 使用默认设置

#### 2.5 添加 Recursive Character Text Splitter 节点
- **节点类型**: Recursive Character Text Splitter
- **名称**: "Text Splitter"
- **配置**:
  - Chunk Size: 1000
  - Chunk Overlap: 200

#### 2.6 连接数据索引节点
连接顺序：
1. HTTP Request → Pinecone Vector Store (main)
2. OpenAI Embeddings → Pinecone Vector Store (ai_embedding)
3. Default Data Loader → Pinecone Vector Store (ai_document)
4. Text Splitter → Default Data Loader (ai_textSplitter)

### 步骤 3: 配置聊天界面部分

#### 3.1 添加 Chat Trigger 节点
- **节点类型**: Chat Trigger
- **名称**: "Chat Trigger"
- **配置**: 使用默认设置

#### 3.2 添加 AI Agent 节点
- **节点类型**: AI Agent
- **名称**: "AI Agent"
- **配置**:
  - Agent Type: Tools Agent
  - System Message: 
    ```
    You are a helpful assistant providing information about the GitHub API and how to use it based on the OpenAPI V3 specifications. You can search through the GitHub API documentation to answer questions about endpoints, parameters, authentication, and usage examples.
    ```

#### 3.3 添加 OpenAI Chat Model 节点（主模型）
- **节点类型**: OpenAI Chat Model
- **名称**: "OpenAI Chat Model"
- **配置**:
  - Model: `gpt-4o-mini`
  - 连接 OpenAI 凭据

#### 3.4 添加 Window Buffer Memory 节点
- **节点类型**: Window Buffer Memory
- **名称**: "Window Buffer Memory"
- **配置**: 使用默认设置

#### 3.5 添加 Vector Store Tool 节点
- **节点类型**: Vector Store Tool
- **名称**: "Vector Store Tool"
- **配置**:
  - Tool Name: `github_api_search`
  - Description: `Use this tool to get information about the GitHub API. This database contains OpenAPI v3 specifications.`
  - Max Results: 4

#### 3.6 添加 Pinecone Vector Store 节点（检索模式）
- **节点类型**: Pinecone Vector Store
- **名称**: "Retrieve from Pinecone"
- **配置**:
  - Operation Mode: Retrieve Documents (For Agent/Chain)
  - Pinecone Index: `github-api-docs`
  - 连接 Pinecone 凭据

#### 3.7 添加 OpenAI Embeddings 节点（用于查询）
- **节点类型**: OpenAI Embeddings
- **名称**: "Query Embeddings"
- **配置**:
  - Model: `text-embedding-3-small`
  - 连接 OpenAI 凭据

#### 3.8 添加 OpenAI Chat Model 节点（摘要模型）
- **节点类型**: OpenAI Chat Model
- **名称**: "OpenAI Summarizer"
- **配置**:
  - Model: `gpt-4o-mini`
  - 连接 OpenAI 凭据

#### 3.9 连接聊天界面节点
连接顺序：
1. Chat Trigger → AI Agent (main)
2. OpenAI Chat Model → AI Agent (ai_languageModel)
3. Window Buffer Memory → AI Agent (ai_memory)
4. Vector Store Tool → AI Agent (ai_tool)
5. Pinecone Vector Store → Vector Store Tool (ai_vectorStore)
6. Query Embeddings → Pinecone Vector Store (ai_embedding)
7. OpenAI Summarizer → Vector Store Tool (ai_languageModel)

### 步骤 4: 测试工作流

#### 4.1 首先运行数据索引
1. 点击 "Fetch GitHub API Spec" 节点
2. 点击 "Execute Node" 运行数据索引流程
3. 等待数据处理完成（可能需要几分钟）
4. 检查 Pinecone 控制台确认数据已索引

#### 4.2 测试聊天功能
1. 点击 "Chat Trigger" 节点
2. 点击底部的 "Chat" 按钮
3. 尝试问一些关于 GitHub API 的问题，例如：
   - "How do I create a GitHub App from a manifest?"
   - "What are the authentication methods for GitHub API?"
   - "How do I list repositories for a user?"

## 故障排除

### 常见问题

1. **Pinecone 连接失败**
   - 检查 API Key 是否正确
   - 确认 Index 名称匹配
   - 验证 Pinecone 区域设置

2. **OpenAI API 错误**
   - 检查 API Key 是否有效
   - 确认账户有足够的配额
   - 验证模型名称正确

3. **数据索引失败**
   - 检查 GitHub API 规范 URL 是否可访问
   - 确认文本分割器配置合理
   - 验证 Pinecone 索引维度匹配

4. **聊天响应质量差**
   - 调整系统消息提示词
   - 增加检索结果数量
   - 优化文本分割参数

### 性能优化

1. **提高响应速度**
   - 减少检索结果数量
   - 使用更小的 embedding 模型
   - 优化文本分割大小

2. **提高回答质量**
   - 改进系统提示词
   - 增加上下文窗口
   - 使用更强的语言模型

## 扩展功能

### 添加更多数据源
- 可以添加更多 HTTP Request 节点来获取其他 API 文档
- 使用不同的 Pinecone 索引来分类存储

### 改进用户体验
- 添加流式响应
- 实现对话历史持久化
- 添加用户身份验证

### 监控和分析
- 添加日志记录节点
- 实现使用统计
- 设置错误告警

## 总结

按照以上步骤，您应该能够成功创建一个功能完整的 GitHub API RAG chatbot。这个 chatbot 能够：

1. 自动索引 GitHub API 文档
2. 理解用户的自然语言问题
3. 检索相关的 API 文档片段
4. 生成准确、有用的回答

记住定期更新数据索引以保持信息的时效性，并根据用户反馈持续优化提示词和配置参数。
