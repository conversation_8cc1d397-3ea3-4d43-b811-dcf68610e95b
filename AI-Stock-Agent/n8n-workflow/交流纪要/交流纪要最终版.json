{"name": "交流纪要最终版", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 7 * * *"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2700, 380], "id": "0c8bfb44-6cc8-4c21-baff-88963c69b853", "name": "每日7点触发"}, {"parameters": {"jsCode": "// 获取今天日期\nconst today = new Date();\nconst daysAgo = 1;\nconst pastDate = new Date();\npastDate.setDate(today.getDate() - daysAgo);\n\nfunction formatDate(date) {\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  return `${year}-${month}-${day}`;\n}\n\nreturn [{\n  json: {\n    dateStart: formatDate(pastDate),\n    dateEnd: formatDate(today)\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2420, 380], "id": "f599bf40-8b2f-473b-82cd-557a54d78216", "name": "时间范围"}, {"parameters": {"url": "https://api.finevent.top/api/agent/medias", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "pageSize", "value": "20"}, {"name": "pageIndex", "value": "1"}, {"name": "source", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "dateStart", "value": "={{ $json.dateStart }}"}, {"name": "dateEnd", "value": "={{ $json.dateEnd }}"}]}, "options": {"redirect": {}, "timeout": 45000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2180, 380], "id": "de498228-96ce-4c33-9919-49963d73d767", "name": "获取数据", "credentials": {"httpHeaderAuth": {"id": "LHPWsusTmsUPyRhu", "name": "FET Header Auth account"}}}, {"parameters": {"jsCode": "// 工具函数：计算Levenshtein距离\nfunction levenshtein(a, b) {\n  const matrix = Array.from({ length: a.length + 1 }, () => []);\n  for (let i = 0; i <= a.length; i++) matrix[i][0] = i;\n  for (let j = 0; j <= b.length; j++) matrix[0][j] = j;\n  for (let i = 1; i <= a.length; i++) {\n    for (let j = 1; j <= b.length; j++) {\n      matrix[i][j] = a[i - 1] === b[j - 1]\n        ? matrix[i - 1][j - 1]\n        : Math.min(\n            matrix[i - 1][j - 1] + 1,\n            matrix[i][j - 1] + 1,\n            matrix[i - 1][j] + 1\n          );\n    }\n  }\n  return matrix[a.length][b.length];\n}\n\n// 工具函数：字符串相似度（1-距离/最大长度）\nfunction similarity(a, b) {\n  if (!a || !b) return 0;\n  const distance = levenshtein(a, b);\n  return 1 - distance / Math.max(a.length, b.length);\n}\n\n// 判断标题是否完整（只要求长度大于6）\nfunction isTitleComplete(title) {\n  return typeof title === 'string' && title.length > 6;\n}\n\n// 计算单条数据质量得分\nfunction calcQualityScore(item) {\n  let score = 1;\n  if (!item.title || !isTitleComplete(item.title)) score -= 0.5;\n  if (!item.content || item.content.replace(/<[^>]+>/g, '').length < 60) score -= 0.5;\n  return Math.max(0, score);\n}\n\n// 主流程\nconst data = $input.first().json;\nif (!data || !data.data || !Array.isArray(data.data.rows)) {\n  return [{ json: { error: 'INVALID_DATA', message: 'API数据结构异常' } }];\n}\n\nconst rows = data.data.rows;\nconst deduped = [];\nconst used = new Array(rows.length).fill(false);\nconst SIM_THRESHOLD = 0.6; // 标题相似度阈值\n\nfor (let i = 0; i < rows.length; i++) {\n  if (used[i]) continue;\n  const item = rows[i];\n  if (!item.title || !isTitleComplete(item.title)) continue;\n  if (!item.content || item.content.replace(/<[^>]+>/g, '').length < 60) continue;\n\n  // 合并相似标题\n  for (let j = i + 1; j < rows.length; j++) {\n    if (used[j]) continue;\n    if (!rows[j].title) continue;\n    if (similarity(item.title, rows[j].title) >= SIM_THRESHOLD) {\n      used[j] = true;\n    }\n  }\n\n  // 计算质量得分\n  const qualityScore = calcQualityScore(item);\n  if (qualityScore < 0.8) continue;\n\n  deduped.push({\n    id: item.id,\n    title: item.title,\n    content: item.content,\n    qualityScore,\n    source: item.source || 'xiaozuowen',\n    timestamp: new Date().toISOString()\n  });\n}\n\nreturn deduped.map(item => ({ json: item }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1940, 380], "id": "24d89cd5-2f58-4bd7-aaa2-5b970d7999bc", "name": "数据验证与预处理"}, {"parameters": {"batchSize": 8, "options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1640, 380], "id": "bbb86f90-305e-46d6-bc3f-5001f5286cfd", "name": "批次分割器"}, {"parameters": {"promptType": "define", "text": "={{ $input.all() }}", "hasOutputParser": true, "options": {"systemMessage": "你是专业的A股数据处理员，专门负责从调研纪要中提取和整理股票信息，不进行价值评估。\n\n### 核心职责\n**数据提取与整理**：\n- 从调研纪要中识别和提取A股上市公司信息\n- 匹配准确的6位股票代码\n- 整理公司名称和相关信息\n- 保持原始内容的完整性\n\n### 股票识别标准\n**识别范围**：\n- A股上市公司（沪深主板、科创板、创业板、中小板）\n- 股票代码格式：严格6位数字（600000-688999, 000001-399999）\n- 公司名称：全称、简称、常用别名\n\n**提取优先级**：\n1. 调研纪要明确提及的公司（最高优先级）\n2. 产业链上下游相关公司\n3. 政策受益或行业变化涉及的公司\n4. 业绩、重组、合作等关键词相关的公司\n\n**处理原则**：\n- 只要内容中提到A股公司就提取，不判断投资价值\n- 保持原始内容完整不变\n- 确保股票代码100%准确\n- 一条记录可以包含多个股票代码\n\n### 输入数据\n批次数据：[{id, content, source, timestamp}, ...]\n\n### 输出格式要求（重要！）\n**严格要求**：必须直接输出纯JSON数组，绝对不能使用任何markdown代码块包装！\n\n**标准格式**（直接输出，不要```json标记）：\n[\n  {\n    \"id\": 123,\n    \"content\": \"完整原始内容\",\n    \"code\": \"600893,000738\",\n    \"companies\": \"航发动力,航发控制\",\n    \"relevance\": 9\n  }\n]\n\n**字段说明**：\n- id: 原始记录ID\n- content: 完整原始内容（必须保持不变）\n- code: 股票代码（多个用逗号分隔）\n- companies: 公司名称（多个用逗号分隔）\n- relevance: 股票与内容的相关性（1-10分）\n\n### 质量要求\n- 股票代码100%准确，格式严格6位数字\n- 保持原始content完整不变\n- 只要提到A股公司就提取，不做价值判断\n- relevance基于股票在内容中的重要程度评估\n\n### 输出要求（必须遵守！）\n- 直接输出JSON数组，从[开始到]结束\n- 绝对不要使用```json或```标记\n- 不要添加任何解释文字或markdown格式\n- 示例：[{\"id\":123,\"content\":\"...\",\"code\":\"600893\",...}]"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1260, 400], "id": "219d12a4-c65a-464f-97ce-667caae4501a", "name": "数据处理分析师"}, {"parameters": {"jsCode": "// 增强AI输出格式清理器 - 处理合并agent的输出\nconst inputItems = $input.all();\nconsole.log('AI输出清理器：接收到的数据项数量:', inputItems.length);\n\nconst cleanedResults = [];\n\nfor (const item of inputItems) {\n  if (item.json?.error) {\n    // 传递错误信息\n    cleanedResults.push(item);\n    continue;\n  }\n  \n  let aiOutput = null;\n  \n  // 处理AI节点的各种输出格式\n  if (item.json?.output) {\n    try {\n      let rawOutput = item.json.output;\n      \n      if (typeof rawOutput === 'string') {\n        console.log('AI输出清理器：原始输出类型为字符串，开始清理');\n        \n        // 更强力的markdown代码块清理\n        rawOutput = rawOutput\n          .replace(/^\\s*```json\\s*/gm, '')  // 移除开头的```json\n          .replace(/^\\s*```\\s*$/gm, '')     // 移除结尾的```\n          .replace(/^\\s*```[a-zA-Z]*\\s*/gm, '') // 移除其他语言标记\n          .trim();\n        \n        console.log('AI输出清理器：清理后的输出:', rawOutput.substring(0, 200) + '...');\n        \n        // 尝试解析JSON\n        aiOutput = JSON.parse(rawOutput);\n        console.log('AI输出清理器：JSON解析成功');\n      } else {\n        console.log('AI输出清理器：原始输出类型为对象，直接使用');\n        aiOutput = rawOutput;\n      }\n    } catch (parseError) {\n      console.error('AI输出清理器：解析失败:', parseError.message);\n      console.error('AI输出清理器：原始输出前500字符:', item.json.output?.substring(0, 500));\n      \n      // 尝试更激进的清理方法\n      try {\n        let fallbackOutput = item.json.output;\n        if (typeof fallbackOutput === 'string') {\n          // 查找JSON数组的开始和结束\n          const startIndex = fallbackOutput.indexOf('[');\n          const endIndex = fallbackOutput.lastIndexOf(']');\n          \n          if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {\n            const jsonPart = fallbackOutput.substring(startIndex, endIndex + 1);\n            console.log('AI输出清理器：尝试提取JSON部分:', jsonPart.substring(0, 200) + '...');\n            aiOutput = JSON.parse(jsonPart);\n            console.log('AI输出清理器：备用解析成功');\n          } else {\n            throw new Error('无法找到有效的JSON数组');\n          }\n        } else {\n          throw new Error('输出不是字符串类型');\n        }\n      } catch (fallbackError) {\n        console.error('AI输出清理器：备用解析也失败:', fallbackError.message);\n        cleanedResults.push({\n          json: {\n            error: 'AI_PARSE_ERROR',\n            message: `AI输出解析失败: ${parseError.message}`,\n            rawOutput: item.json.output?.substring(0, 1000) // 限制错误输出长度\n          }\n        });\n        continue;\n      }\n    }\n  } else if (Array.isArray(item.json)) {\n    console.log('AI输出清理器：输入已经是数组格式');\n    aiOutput = item.json;\n  } else {\n    console.warn('AI输出清理器：未识别的输出格式:', typeof item.json);\n    cleanedResults.push({\n      json: {\n        error: 'UNKNOWN_FORMAT',\n        message: '未识别的AI输出格式',\n        rawOutput: item.json\n      }\n    });\n    continue;\n  }\n  \n  // 验证和清理AI输出\n  if (Array.isArray(aiOutput)) {\n    console.log(`AI输出清理器：成功解析到 ${aiOutput.length} 条记录`);\n    \n    // 验证每条记录的必要字段\n    let validRecords = 0;\n    for (const record of aiOutput) {\n      if (record && typeof record === 'object') {\n        // 检查合并agent输出的必要字段\n        if (record.id && record.content && (record.code || record.score)) {\n          cleanedResults.push({ json: record });\n          validRecords++;\n        } else {\n          console.warn('AI输出清理器：记录缺少必要字段:', {\n            hasId: !!record.id,\n            hasContent: !!record.content,\n            hasCode: !!record.code,\n            hasScore: !!record.score\n          });\n        }\n      } else {\n        console.warn('AI输出清理器：无效的记录格式:', typeof record);\n      }\n    }\n    \n    console.log(`AI输出清理器：有效记录数量: ${validRecords}/${aiOutput.length}`);\n  } else {\n    console.warn('AI输出清理器：AI输出不是数组格式:', typeof aiOutput);\n    cleanedResults.push({\n      json: {\n        error: 'INVALID_ARRAY',\n        message: 'AI输出不是数组格式',\n        rawOutput: aiOutput\n      }\n    });\n  }\n}\n\nconsole.log(`AI输出清理器：处理完成，输出 ${cleanedResults.length} 项`);\nreturn cleanedResults;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-720, 400], "id": "deb54607-065b-4c64-a51e-f5a3b4ad0405", "name": "AI输出清理器"}, {"parameters": {"jsCode": "// 结果汇总器 - 纯汇总功能，不进行任何筛选\nconst inputItems = $input.all();\nconsole.log('结果汇总器：接收到的数据项数量:', inputItems.length);\n\nconst allResults = [];\nconst seenCombinations = new Set();\nconst errors = [];\nlet totalProcessed = 0;\n\nfor (const item of inputItems) {\n  totalProcessed++;\n  \n  // 检查错误\n  if (item.json?.error) {\n    errors.push({\n      batch: totalProcessed,\n      error: item.json.error,\n      message: item.json.message\n    });\n    continue;\n  }\n  \n  // 处理数据处理员的输出（纯汇总，不筛选）\n  const result = item.json;\n  if (result && result.id && result.code && result.content) {\n    // 基础验证股票代码格式（确保数据完整性）\n    const codes = result.code.split(',').map(code => code.trim()).filter(code => \n      code && /^[0-9]{6}$/.test(code)\n    );\n    \n    if (codes.length > 0) {\n      const combination = `${result.id}-${codes.join(',')}`;\n      \n      // 去重处理\n      if (!seenCombinations.has(combination)) {\n        seenCombinations.add(combination);\n        \n        // 保留所有数据，不进行任何筛选\n        allResults.push({\n          id: result.id,\n          content: result.content, // 保留完整内容\n          code: codes.join(','),\n          companies: result.companies || '',\n          relevance: result.relevance || 5,\n          timestamp: new Date().toISOString()\n        });\n      } else {\n        console.log(`结果汇总器：跳过重复记录 ID ${result.id}`);\n      }\n    } else {\n      console.warn(`结果汇总器：ID ${result.id} 的股票代码格式无效:`, result.code);\n    }\n  } else if (result && !result.error) {\n    console.warn('结果汇总器：记录缺少必要字段:', {\n      hasId: !!result.id,\n      hasCode: !!result.code,\n      hasContent: !!result.content\n    });\n  }\n}\n\n// 记录处理结果\nif (errors.length > 0) {\n  console.warn(`结果汇总器：发现 ${errors.length} 个批次错误:`, errors);\n}\n\nconsole.log(`结果汇总器：处理了 ${totalProcessed} 个数据项，汇总了 ${allResults.length} 条数据`);\nconsole.log('结果汇总器：示例结果:', allResults.slice(0, 2));\n\nif (allResults.length === 0) {\n  console.warn('结果汇总器：没有接收到有效数据');\n  return [{ json: { error: 'NO_DATA', message: '没有接收到有效的股票数据' } }];\n}\n\n// 按相关性和ID排序（不按评分，因为没有评分）\nallResults.sort((a, b) => {\n  if (a.relevance !== b.relevance) {\n    return b.relevance - a.relevance; // 按相关性降序\n  }\n  // 按ID排序作为最后的排序标准\n  return a.id - b.id;\n});\n\n// 生成数据摘要用于日志记录\nconst summaryData = {\n  totalItems: allResults.length,\n  avgRelevance: allResults.reduce((sum, item) => sum + item.relevance, 0) / allResults.length,\n  timestamp: new Date().toISOString()\n};\n\nconsole.log('结果汇总器：数据摘要:', summaryData);\n\n// 直接返回所有股票数据\n// 股票分析师将对这些数据进行价值评估和筛选\nreturn allResults.map(item => ({ json: item }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-520, 20], "id": "c2e7f382-967b-48f4-b25e-ef333672d205", "name": "结果汇总器"}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-160, 0], "id": "436baac3-2f89-44c9-9c72-a247d58510b4", "name": "请求分割器"}, {"parameters": {"method": "POST", "url": "https://api.finevent.top/api/agent/medias/stock", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "mediaId", "value": "={{ $json.id }}"}, {"name": "code", "value": "={{ $json.code }}"}]}, "options": {"redirect": {}, "timeout": 20000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 20], "id": "********-0be2-46fb-b70c-4afe3d32521e", "name": "股票请求", "credentials": {"httpHeaderAuth": {"id": "LHPWsusTmsUPyRhu", "name": "FET Header Auth account"}}}, {"parameters": {"jsCode": "// 重要性判断与去重节点\n// 此节点负责根据相关性评分筛选高价值数据\nconst inputItems = $input.all();\nconsole.log('重要性判断与去重：接收到数据项数量:', inputItems.length);\n\nif (inputItems.length === 0) {\n    console.warn('重要性判断与去重：没有接收到数据');\n    return [];\n}\n\n// --- 配置 ---\nconst IMPORTANCE_THRESHOLD = 7; // 重要性阈值\n\nconst highValueOutput = []; // 存放高价值的完整n8n items\nconst processedIds = new Set(); // 用于去重\n\nfor (const item of inputItems) {\n    const data = item.json;\n\n    // 检查数据有效性\n    if (!data || !data.id || typeof data.relevance === 'undefined') {\n        console.warn('重要性判断与去重：数据格式不完整，跳过', data);\n        continue;\n    }\n\n    // 去重检查\n    if (processedIds.has(data.id)) {\n        console.log(`重要性判断与去重：媒体ID ${data.id} 已处理，跳过重复项`);\n        continue;\n    }\n\n    const relevance = data.relevance;\n\n    // 重要性判断\n    if (relevance >= IMPORTANCE_THRESHOLD) {\n        console.log(`重要性判断与去重：媒体ID ${data.id} (相关性 ${relevance}) 判定为高价值`);\n        processedIds.add(data.id);\n        highValueOutput.push(item);\n    } else {\n        console.log(`重要性判断与去重：媒体ID ${data.id} (相关性 ${relevance}) 低于阈值，跳过`);\n    }\n}\n\nconsole.log(`重要性判断与去重：筛选出 ${highValueOutput.length} 条高价值数据`);\n\n// 返回高价值数据\nreturn highValueOutput;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-100, 400], "id": "2e65dbba-734f-4595-853b-e753245ca881", "name": "重要性判断与去重"}, {"parameters": {"method": "PUT", "url": "=https://api.finevent.top/api/agent/medias/{{ $('单个请求').item.json.id }}}}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "important", "value": "true"}]}, "options": {"redirect": {}, "timeout": 20000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1300, 400], "id": "c7d474bf-76bb-4d99-a7a8-2282c68a1b4d", "name": "更新媒体重要性", "credentials": {"httpHeaderAuth": {"id": "LHPWsusTmsUPyRhu", "name": "FET Header Auth account"}}}, {"parameters": {"method": "POST", "url": "https://api.finevent.top/api/agent/messages/send", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "type", "value": "media"}, {"name": "id", "value": "={{ $('单个请求').item.json.id }}"}]}, "options": {"redirect": {}, "timeout": 20000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 400], "id": "6ebd6af1-9c66-45ca-975d-b0069f5d65d8", "name": "发送消息", "credentials": {"httpHeaderAuth": {"id": "LHPWsusTmsUPyRhu", "name": "FET Header Auth account"}}}, {"parameters": {"promptType": "define", "text": "={{ $input.all() }}", "hasOutputParser": true, "options": {"systemMessage": "你是一位专业的A股板块分析师，专注于从调研纪要中识别板块投资机会，严格聚焦板块层面的投资逻辑分析。\n\n### 核心职责\n**第一步：价值评估筛选**\n对每条股票数据进行板块投资价值评估（总分10分）：\n- 信息独家性（0-3分）：独家板块消息3分，行业内部信息2分，公开解读1分\n- 板块影响力（0-3分）：板块轮动机会3分，行业催化剂2分，产业趋势1分\n- 投资时效性（0-2分）：近期板块催化因素2分，中长期行业价值1分\n- 可操作性（0-2分）：板块方向明确2分，行业趋势清晰1分\n\n**筛选标准**：只有评分>=7分的高价值板块信息才纳入分析报告\n\n**第二步：板块分析报告**\n基于筛选出的高价值数据，撰写专业的板块投资分析\n\n### 分析聚焦点\n- **板块轮动逻辑**：分析板块轮动的驱动因素和时机\n- **行业催化事件**：识别影响整个行业的重要催化因素\n- **产业政策导向**：解读政策对板块的影响和机会\n- **技术变革趋势**：分析技术进步对行业格局的影响\n- **供需关系变化**：评估行业供需格局的变化趋势\n\n### 严格要求\n**内容聚焦**：\n- 严格聚焦板块和行业层面，不涉及具体个股\n- 不进行与板块无关的宏观经济泛泛而谈\n- 不添加与调研纪要内容无关的自由发挥\n- 基于实际数据内容进行分析，不编造信息\n\n**分析深度**：\n- 深入分析板块投资逻辑的核心驱动因素\n- 识别板块内的细分机会和投资主线\n- 评估板块投资的风险点和不确定性\n- 提供板块配置的时机判断\n\n### 输出要求\n**重要：必须只输出一个完整的分析报告，不要输出多个分析结果**\n\n**写作要求**：\n写成一篇自然的板块分析文章，不使用任何标题、列表或格式化结构。每个段落独立阐述一个板块投资主题，段落之间没有衔接关系，各自专注于自己的板块投资逻辑。严格避免使用\"首先\"、\"其次\"、\"再次\"、\"最后\"、\"另外\"、\"同时\"、\"此外\"等任何衔接词。\n\n**段落要求**：\n- 每个段落独立成篇，专注一个板块投资主题\n- 段落开头直接进入板块投资逻辑，不需要过渡词汇\n- 避免任何形式的序号、衔接词或过渡语句\n- 每段围绕板块核心逻辑展开，包含行业支撑因素和催化事件\n- 风险提示段落同样独立，直接阐述板块风险点\n\n### 严格禁止\n- **禁止提及具体股票代码或公司名称**\n- **禁止脱离调研纪要内容的自由发挥**\n- **禁止泛泛而谈的宏观经济分析**\n- **禁止无关的市场情绪描述**\n- **禁止做具体价格和时间预测**\n- **禁止使用标题、列表、格式化结构**\n- **禁止使用任何衔接词和过渡语句**\n- **禁止输出多个分析结果或多个输出项**\n\n### 分析框架\n1. **板块筛选**：从所有数据中筛选出>=7分的高价值板块信息\n2. **主题归纳**：将高价值信息按板块投资主题分类\n3. **逻辑梳理**：分析板块投资逻辑的核心驱动因素\n4. **机会识别**：找出最具投资价值的板块方向\n5. **风险评估**：客观分析板块投资的潜在风险\n\n### 篇幅控制\n800-1000字，确保内容聚焦板块分析，逻辑清晰，每个段落独立完整，严格基于调研纪要内容进行分析。\n\n### 最终输出格式\n直接输出一篇完整的板块分析文章，不要使用任何JSON格式或其他结构化格式。只输出纯文本内容，确保只有一个输出结果。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [480, 780], "id": "96841967-8dbe-4338-9965-ca99f81244b2", "name": "股票分析师"}, {"parameters": {"jsCode": "// 转换AI分析结果为markdown格式并准备发布数据\nconst inputItems = $input.all();\nconsole.log('内容转换器：接收到的数据项数量:', inputItems.length);\n\nif (inputItems.length === 0) {\n  return [{ json: { error: 'NO_DATA', message: '没有接收到AI分析结果' } }];\n}\n\n// 获取AI分析结果\nconst analysisResult = inputItems[0].json;\nlet content = '';\n\n// 处理AI分析结果\nif (typeof analysisResult === 'string') {\n  content = analysisResult;\n} else if (analysisResult && analysisResult.output) {\n  content = analysisResult.output;\n} else if (analysisResult && analysisResult.text) {\n  content = analysisResult.text;\n} else {\n  console.warn('内容转换器：无法识别的AI输出格式:', analysisResult);\n  content = JSON.stringify(analysisResult);\n}\n\n// 清理内容\ncontent = content.trim();\nif (!content) {\n  return [{ json: { error: 'EMPTY_CONTENT', message: 'AI分析结果为空' } }];\n}\n\n// 转换为markdown格式（保持原有格式，因为AI已经输出了自然文章格式）\nconst markdownContent = content;\n\n// 生成今天的日期\nconst today = new Date();\nconst dateString = today.getFullYear().toString() + \n                  (today.getMonth() + 1).toString().padStart(2, '0') + \n                  today.getDate().toString().padStart(2, '0');\n\n// 提取标题（取内容的前20个字符）\nconst title = content.substring(0, 20).replace(/\\n/g, ' ').trim();\n\n// 准备发布数据\nconst postData = {\n  category: 'dailystory',\n  status: 'published',\n  time: dateString,\n  title: title,\n  content: markdownContent\n};\n\nconsole.log('内容转换器：准备发布的数据:', {\n  category: postData.category,\n  status: postData.status,\n  time: postData.time,\n  title: postData.title,\n  contentLength: postData.content.length\n});\n\nreturn [{ json: postData }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 780], "id": "559a2b0e-bbd5-4203-a01e-9c7bb4a718ad", "name": "内容转换器"}, {"parameters": {"method": "POST", "url": "https://api.finevent.top/api/agent/posts", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "category", "value": "={{ $json.category }}"}, {"name": "status", "value": "={{ $json.status }}"}, {"name": "time", "value": "={{ $json.time }}"}, {"name": "title", "value": "={{ $json.title }}"}, {"name": "content", "value": "={{ $json.content }}"}]}, "options": {"redirect": {}, "timeout": 30000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1260, 780], "id": "e698775a-54f1-430d-b800-7522b720a5b1", "name": "创建文章", "credentials": {"httpHeaderAuth": {"id": "LHPWsusTmsUPyRhu", "name": "FET Header Auth account"}}}, {"parameters": {"method": "POST", "url": "https://api.finevent.top/api/agent/operate/share", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "postId", "value": "={{ $json.data.id }}"}, {"name": "type", "value": "xueqiu,weibo"}]}, "options": {"redirect": {}, "timeout": 30000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1580, 780], "id": "b7104d99-c27e-49dd-a699-009fd1cee872", "name": "分享到雪球", "credentials": {"httpHeaderAuth": {"id": "LHPWsusTmsUPyRhu", "name": "FET Header Auth account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [540, 400], "id": "a82b6d49-728c-4f66-abf7-e99eb9af3ab7", "name": "单个请求"}, {"parameters": {"url": "=https://api.finevent.top/api/agent/medias/{{ $json.id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"redirect": {}, "timeout": 20000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [820, 420], "id": "0df9070f-b618-4ffb-9763-9224e4a540a8", "name": "查询重要性", "credentials": {"httpHeaderAuth": {"id": "LHPWsusTmsUPyRhu", "name": "FET Header Auth account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "62bf9f6d-934c-4d3a-b711-07fe14fdab37", "leftValue": "={{ $json.data.important }}", "rightValue": "0", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1020, 420], "id": "6c645631-7d29-4ee7-8e1f-fdd461375142", "name": "If"}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1260, 780], "id": "2b76f6b5-d17b-4858-8dc4-845af3017a0a", "name": "Google Gemini 2.0", "credentials": {"googlePalmApi": {"id": "yJdzdWA9MSGlauul", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [480, 1020], "id": "27c6d100-dba2-43e4-89f6-0e3132a0533e", "name": "Google Gemini 2.5", "credentials": {"googlePalmApi": {"id": "yJdzdWA9MSGlauul", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"jsCode": "// 高价值信息筛选与分发节点（已修正）\n// 此节点负责根据相关性评分筛选数据，并将其分发到不同的处理路径\nconst inputItems = $input.all();\nconsole.log('筛选与分发：接收到数据项数量:', inputItems.length);\n\nif (inputItems.length === 0) {\n    console.warn('筛选与分发：没有接收到数据');\n    // 确保两个输出都有一个（空的）返回，以避免工作流出错\n    return [[], []];\n}\n\n// --- 配置 ---\nconst IMPORTANCE_THRESHOLD = 7; // 重要性阈值\n\nconst highValueOutput = []; // 存放高价值的完整n8n items\nconst lowValueItems = [];   // 存放低价值的数据用于报告\n\nfor (const item of inputItems) {\n    const data = item.json;\n\n    // 检查数据有效性\n    if (!data || !data.id || typeof data.relevance === 'undefined') {\n        console.warn('筛选与分发：数据格式不完整，跳过', data);\n        lowValueItems.push({\n            reason: 'invalid_data',\n            data: data\n        });\n        continue;\n    }\n\n    const relevance = data.relevance;\n\n    // 1. 重要性判断\n    if (relevance >= IMPORTANCE_THRESHOLD) {\n        console.log(`筛选与分发：媒体ID ${data.id} (相关性 ${relevance}) 判定为高价值`);\n        // 直接推送原始的、符合n8n格式的item\n        highValueOutput.push(item);\n    } else {\n        lowValueItems.push({\n            reason: 'low_importance',\n            mediaId: data.id,\n            relevance: relevance\n        });\n    }\n}\n\nconsole.log(`筛选与分发：高价值数据 ${highValueOutput.length} 条，低价值数据 ${lowValueItems.length} 条`);\n\n// --- 准备输出 ---\n\n// 输出1: 高价值数据，已经是正确的 [{json:{...}},...]] 格式\n\n// 输出2: 低价值数据的报告\nconst lowValueReportOutput = [{\n    json: {\n        skipped: lowValueItems,\n        stats: {\n            total: inputItems.length,\n            highValue: highValueOutput.length,\n            lowValue: lowValueItems.length\n        }\n    }\n}];\n\n// 返回两个数组，分别对应两个输出\nreturn highValueOutput"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-160, 780], "id": "ebce41b1-9729-457c-a173-50b904a57d18", "name": "重要性筛选"}], "pinData": {}, "connections": {"每日7点触发": {"main": [[{"node": "时间范围", "type": "main", "index": 0}]]}, "时间范围": {"main": [[{"node": "获取数据", "type": "main", "index": 0}]]}, "获取数据": {"main": [[{"node": "数据验证与预处理", "type": "main", "index": 0}]]}, "数据验证与预处理": {"main": [[{"node": "批次分割器", "type": "main", "index": 0}]]}, "批次分割器": {"main": [[{"node": "结果汇总器", "type": "main", "index": 0}], [{"node": "数据处理分析师", "type": "main", "index": 0}]]}, "数据处理分析师": {"main": [[{"node": "AI输出清理器", "type": "main", "index": 0}]]}, "AI输出清理器": {"main": [[{"node": "批次分割器", "type": "main", "index": 0}]]}, "结果汇总器": {"main": [[{"node": "请求分割器", "type": "main", "index": 0}, {"node": "重要性判断与去重", "type": "main", "index": 0}, {"node": "重要性筛选", "type": "main", "index": 0}]]}, "请求分割器": {"main": [[], [{"node": "股票请求", "type": "main", "index": 0}]]}, "股票请求": {"main": [[{"node": "请求分割器", "type": "main", "index": 0}]]}, "重要性判断与去重": {"main": [[{"node": "单个请求", "type": "main", "index": 0}]]}, "更新媒体重要性": {"main": [[{"node": "发送消息", "type": "main", "index": 0}]]}, "发送消息": {"main": [[{"node": "单个请求", "type": "main", "index": 0}]]}, "股票分析师": {"main": [[{"node": "内容转换器", "type": "main", "index": 0}]]}, "内容转换器": {"main": [[{"node": "创建文章", "type": "main", "index": 0}]]}, "创建文章": {"main": [[{"node": "分享到雪球", "type": "main", "index": 0}]]}, "单个请求": {"main": [[], [{"node": "查询重要性", "type": "main", "index": 0}]]}, "查询重要性": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "更新媒体重要性", "type": "main", "index": 0}]]}, "Google Gemini 2.0": {"ai_languageModel": [[{"node": "数据处理分析师", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini 2.5": {"ai_languageModel": [[{"node": "股票分析师", "type": "ai_languageModel", "index": 0}]]}, "重要性筛选": {"main": [[{"node": "股票分析师", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "e0ad4777-cf85-4f24-8400-b5dbf95336d9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "30c0bdaad6d4d5b07c212ffc16b6864560a9f521b502a964192acb3b68d1eabc"}, "id": "0F7PHYgrGxYxkCdx", "tags": []}