{"name": "Simple Test Workflow", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "id": "manual-trigger-test", "name": "Manual Trigger"}, {"parameters": {"url": "https://raw.githubusercontent.com/github/rest-api-description/refs/heads/main/descriptions/api.github.com/api.github.com.json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "id": "http-request-test", "name": "Fetch GitHub API Spec"}], "connections": {"Manual Trigger": {"main": [[{"node": "Fetch GitHub API Spec", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": []}