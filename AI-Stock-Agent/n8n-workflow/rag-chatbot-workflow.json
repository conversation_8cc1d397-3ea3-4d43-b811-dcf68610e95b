{"name": "GitHub API RAG Chatbot", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "id": "manual-trigger-1", "name": "Manual Trigger"}, {"parameters": {"url": "https://raw.githubusercontent.com/github/rest-api-description/refs/heads/main/descriptions/api.github.com/api.github.com.json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "id": "http-request-github-api", "name": "Fetch GitHub API Spec"}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "github-api-docs", "mode": "name"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [460, 300], "id": "pinecone-vector-store-insert", "name": "Save to Pinecone Vector Store"}, {"parameters": {"model": "text-embedding-3-small"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [460, 420], "id": "embeddings-openai-insert", "name": "Generate Embeddings"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [460, 540], "id": "default-data-loader", "name": "Default Data Loader"}, {"parameters": {"chunkSize": 1000, "chunkOverlap": 200}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [460, 660], "id": "text-splitter", "name": "Recursive Character Text Splitter"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1, "position": [800, 300], "id": "chat-trigger", "name": "<PERSON><PERSON>"}, {"parameters": {"agent": "toolsAgent", "systemMessage": "You are a helpful assistant providing information about the GitHub API and how to use it based on the OpenAPI V3 specifications. You can search through the GitHub API documentation to answer questions about endpoints, parameters, authentication, and usage examples."}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [1020, 300], "id": "ai-agent", "name": "AI Agent"}, {"parameters": {"model": "gpt-4o-mini"}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1020, 420], "id": "openai-chat-model", "name": "OpenAI Chat Model"}, {"parameters": {"sessionIdType": "fromInput", "sessionKey": "sessionId"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1, "position": [1020, 540], "id": "window-buffer-memory", "name": "Window Buffer Memory"}, {"parameters": {"name": "github_api_search", "description": "Use this tool to get information about the GitHub API. This database contains OpenAPI v3 specifications.", "maxResults": 4}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [1240, 300], "id": "vector-store-tool", "name": "Vector Store Tool"}, {"parameters": {"mode": "retrieve", "pineconeIndex": {"__rl": true, "value": "github-api-docs", "mode": "name"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1460, 300], "id": "pinecone-vector-store-retrieve", "name": "Retrieve from Pinecone"}, {"parameters": {"model": "text-embedding-3-small"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [1460, 420], "id": "embeddings-openai-retrieve", "name": "Query Embeddings"}, {"parameters": {"model": "gpt-4o-mini"}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1460, 540], "id": "openai-summarizer", "name": "OpenAI Summarizer"}], "connections": {"Manual Trigger": {"main": [[{"node": "Fetch GitHub API Spec", "type": "main", "index": 0}]]}, "Fetch GitHub API Spec": {"main": [[{"node": "Save to Pinecone Vector Store", "type": "main", "index": 0}]]}, "Generate Embeddings": {"ai_embedding": [[{"node": "Save to Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Save to Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Retrieve from Pinecone": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Query Embeddings": {"ai_embedding": [[{"node": "Retrieve from Pinecone", "type": "ai_embedding", "index": 0}]]}, "OpenAI Summarizer": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": []}